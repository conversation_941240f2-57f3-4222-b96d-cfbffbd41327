{"name": "basic-wap", "version": "0.1.0", "private": true, "scripts": {"lint": "vue-cli-service lint", "serve-dev": "vue-cli-service serve --mode development", "build-dev": "vue-cli-service build --mode development", "serve-pro": "vue-cli-service serve --mode production", "build-pro": "vue-cli-service build --mode production"}, "dependencies": {"axios": "^0.19.0", "china-tax-calculator": "^1.0.1", "core-js": "^3.6.5", "exif-js": "^2.3.0", "pdfh5": "^2.0.5", "vant": "^2.10.3", "vue": "^2.6.11", "vue-pdf": "^4.3.0", "vue-router": "^3.2.0", "vuex": "^3.4.0", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.0", "@vue/cli-plugin-eslint": "^4.5.0", "@vue/cli-service": "^4.5.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "html-webpack-plugin": "3.2.0", "less": "^3.0.4", "less-loader": "^5.0.0", "postcss-px-to-viewport": "^1.1.1", "vue-template-compiler": "^2.6.11"}}
<template>
  <div class="callback-container">
    <div class="result-card">
      <!-- 结果状态显示 -->
      <div class="status-section">
        <div v-if="isSuccess" class="success-status">
          <van-icon name="checked" size="60" color="#07c160" />
          <h2>人脸核身成功</h2>
          <p>恭喜您，人脸核身验证通过！</p>
        </div>
        <div v-else class="error-status">
          <van-icon name="close" size="60" color="#ee0a24" />
          <h2>人脸核身失败</h2>
          <p>很抱歉，人脸核身验证未通过，请重试</p>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import requestHTAPI from '@/axios/HantangTax'
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'FaceCallback',
  data () {
    return {
      params: {
        orderNo: '',
        code: '',
        signature: '',
        newSignature: '',
        liveRate: '',
        h5faceId: ''
      },
      isSuccess: false
    }
  },
  computed: {
    ...mapGetters([
      'getFaceSessionData',
      'getContractData',
      'getSignatureData'
    ])
  },
  mounted () {
    this.parseUrlParams()
    this.checkFaceResult()
  },
  methods: {
    ...mapActions([
      'clearAllContractData'
    ]),
    // 解析URL参数
    parseUrlParams () {
      const urlParams = new URLSearchParams(window.location.search)
      this.params = {
        orderNo: urlParams.get('orderNo') || '',
        code: urlParams.get('code') || '',
        signature: urlParams.get('signature') || '',
        newSignature: urlParams.get('newSignature') || '',
        liveRate: urlParams.get('liveRate') || '',
        h5faceId: urlParams.get('h5faceId') || ''
      }

      console.log('人脸识别回调参数:', this.params)
    },

    // 检查人脸核身结果
    checkFaceResult () {
      // code为'0'表示成功，其他为失败
      this.isSuccess = this.params.code === '0'

      if (this.isSuccess) {
        console.log('人脸核身成功！准备调用fxqFaceResult接口...')
        this.$toast.success('人脸核身成功！')
        // 人脸识别成功后立即调用fxqFaceResult接口
        this.callFxqFaceResultAPI()
      } else {
        console.log('人脸核身失败，错误码:', this.params.code)
        this.$toast.fail('人脸核身失败')
        // 人脸识别失败，显示弹窗让用户选择
        this.showFailureDialog()
      }
    },

    // 调用fxqFaceResult接口
    async callFxqFaceResultAPI () {
      try {
        console.log('=== 调用fxqFaceResult接口 ===')

        const faceResultParams = {
          orderNo: this.params.orderNo,
          code: this.params.code,
          signature: this.params.signature,
          newSignature: this.params.newSignature,
          liveRate: this.params.liveRate,
          h5faceId: this.params.h5faceId
        }

        console.log('调用fxqFaceResult接口参数:', faceResultParams)

        const response = await requestHTAPI.fxqFaceResult(faceResultParams)

        if (response.data.code === '0000') {
          console.log('fxqFaceResult接口调用成功:', response.data)
          // fxqFaceResult调用成功后，继续调用fxqTwoStep接口
          this.callFxqTwoStepAPI()
        } else {
          console.error('fxqFaceResult接口调用失败:', response.data.message)
          this.$toast.fail('人脸识别结果处理失败')
        }
      } catch (error) {
        console.error('fxqFaceResult接口调用异常:', error)
        this.$toast.fail('人脸识别结果处理异常')
      }
    },

    // 处理成功情况
    handleSuccess () {
      console.log('=== 人脸核身成功，准备跳转到首页 ===')
      this.clearSessionData()
      this.$router.push('/')
    },

    // 显示失败弹窗
    showFailureDialog () {
      this.$dialog.confirm({
        title: '人脸识别失败',
        message: '人脸识别验证失败，请选择下一步操作',
        confirmButtonText: '重新签署',
        cancelButtonText: '返回首页'
      }).then(() => {
        // 用户选择重新签署
        this.handleRetry()
      }).catch(() => {
        // 用户选择返回首页
        this.goToHome()
      })
    },

    // 处理重试
    handleRetry () {
      console.log('用户选择重新验证')
      this.$toast('请重新进行人脸验证')
      this.$router.push('/mine/contract')
    },

    // 返回首页
    goToHome () {
      console.log('用户选择返回首页')
      this.clearSessionData()
      this.$router.push('/')
    },

    // 清除Vuex中的合同数据
    clearSessionData () {
      this.clearAllContractData()
      console.log('已清除所有合同相关数据')
    },

    // 调用fxqTwoStep接口
    async callFxqTwoStepAPI () {
      try {
        // 从 Vuex 获取会话数据
        const faceSessionData = this.getFaceSessionData
        const contractData = this.getContractData
        const signatureData = this.getSignatureData

        if (!faceSessionData || !contractData || !signatureData) {
          console.error('会话数据丢失')
          this.$toast.fail('会话数据丢失，请重新签署')
          this.goToHome()
          return
        }

        console.log('恢复的会话数据:', { faceSessionData, contractData, signatureData })

        this.$toast.loading({
          message: '正在完成签署...',
          duration: 0
        })

        const params = {
          base64: signatureData || '',
          dataList: contractData?.dataList || [],
          id: contractData?.id
        }

        console.log('调用fxqTwoStep接口参数:', params)

        const response = await requestHTAPI.fxqTwoStep(params)

        if (response.data.code === '0000') {
          this.$toast.success('合同签署成功！')

          // 清除sessionStorage数据并跳转到首页
          this.clearSessionData()
          setTimeout(() => {
            this.$router.push('/')
          }, 1500)
        } else {
          this.$toast.fail(response.data.message || '合同签署失败')
          this.handleRetry()
        }
      } catch (error) {
        console.error('fxqTwoStep接口调用失败:', error)
        // 接口调用失败，显示弹窗让用户选择
        this.$dialog.confirm({
          title: '签署失败',
          message: '合同签署失败，请选择下一步操作',
          confirmButtonText: '重新签署',
          cancelButtonText: '返回首页'
        }).then(() => {
          // 用户选择重新签署
          this.handleRetry()
        }).catch(() => {
          // 用户选择返回首页
          this.goToHome()
        })
      }
    }

  }
}
</script>

<style scoped>
.callback-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.result-card {
  background: white;
  border-radius: 12px;
  padding: 30px 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.status-section {
  text-align: center;
  margin-bottom: 30px;
}

.success-status h2 {
  color: #07c160;
  margin: 15px 0 10px 0;
  font-size: 20px;
}

.error-status h2 {
  color: #ee0a24;
  margin: 15px 0 10px 0;
  font-size: 20px;
}

.success-status p,
.error-status p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.params-section {
  margin-bottom: 30px;
}

.params-section h3 {
  font-size: 16px;
  color: #333;
  margin: 0 0 15px 0;
  text-align: center;
}

.action-section {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .callback-container {
    padding: 10px;
  }

  .result-card {
    padding: 20px 15px;
  }

  .success-status h2,
  .error-status h2 {
    font-size: 18px;
  }
}
</style>

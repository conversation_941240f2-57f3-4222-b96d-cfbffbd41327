import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

// 创建持久化插件
const createPersistedState = (key) => {
  return (store) => {
    // 从localStorage恢复状态
    const savedState = localStorage.getItem(key)
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState)
        store.replaceState(Object.assign({}, store.state, parsedState))
      } catch (e) {
        console.error('Failed to parse saved state:', e)
        localStorage.removeItem(key)
      }
    }

    // 监听状态变化并保存到localStorage
    store.subscribe((mutation, state) => {
      try {
        localStorage.setItem(key, JSON.stringify({
          contractData: state.contractData,
          signatureData: state.signatureData,
          faceSessionData: state.faceSessionData
        }))
      } catch (e) {
        console.error('Failed to save state to localStorage:', e)
      }
    })
  }
}

export default new Vuex.Store({
  plugins: [createPersistedState('vuex-contract-state')],
  state: {
    // 合同签署相关状态
    contractData: null,
    signatureData: null,
    faceSessionData: null
  },
  mutations: {
    // 设置合同数据
    SET_CONTRACT_DATA (state, data) {
      state.contractData = data
    },
    // 设置签名数据
    SET_SIGNATURE_DATA (state, data) {
      state.signatureData = data
    },
    // 设置人脸识别会话数据
    SET_FACE_SESSION_DATA (state, data) {
      state.faceSessionData = data
    },

    // 清除所有合同相关数据
    CLEAR_CONTRACT_DATA (state) {
      state.contractData = null
      state.signatureData = null
      state.faceSessionData = null
      // 同时清除localStorage中的持久化数据
      localStorage.removeItem('vuex-contract-state')
    }
  },
  actions: {
    // 保存合同数据
    saveContractData ({ commit }, data) {
      commit('SET_CONTRACT_DATA', data)
    },
    // 保存签名数据
    saveSignatureData ({ commit }, data) {
      commit('SET_SIGNATURE_DATA', data)
    },
    // 保存人脸识别会话数据
    saveFaceSessionData ({ commit }, data) {
      commit('SET_FACE_SESSION_DATA', data)
    },

    // 清除所有数据
    clearAllContractData ({ commit }) {
      commit('CLEAR_CONTRACT_DATA')
    }
  },
  getters: {
    // 获取合同数据
    getContractData: state => state.contractData,
    // 获取签名数据
    getSignatureData: state => state.signatureData,
    // 获取人脸识别会话数据
    getFaceSessionData: state => state.faceSessionData,
    // 检查是否有完整的签署数据
    hasCompleteSignData: state => {
      return state.contractData && state.signatureData
    }
  },
  modules: {
  }
})

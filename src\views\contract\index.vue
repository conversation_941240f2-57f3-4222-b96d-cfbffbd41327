<template>
  <div class="contract-container">
    <div class="pdf-viewer-container">
      <div class="pdf-toolbar">
        <div class="toolbar-left">
          <van-button @click="goBack">
            返回
          </van-button>
          <!-- 多PDF时显示标签页切换按钮 -->
          <van-button v-if="pdfList.length > 1" @click="toggleTabView" type="default">
            {{ showTabView ? '隐藏标签' : '显示标签' }}
          </van-button>
        </div>
        <div class="toolbar-center">
          <span class="pdf-title">{{ getCurrentPdfName() }}</span>
        </div>
        <div class="toolbar-right">
          <van-button @click="previousPage" :disabled="currentPage <= 1">
           上一页
          </van-button>
          <van-button @click="nextPage" :disabled="currentPage >= totalPages">
          下一页
          </van-button>
          <!-- 最后一页显示签署按钮 -->
          <van-button type="primary" @click="showConfirmDialog">
            签署
          </van-button>
        </div>
      </div>

      <!-- 多PDF时显示标签页 -->
      <div v-if="pdfList.length > 1 && showTabView" class="pdf-tabs-container">
        <van-tabs v-model="currentPdfIndex" @click="onTabClick" sticky>
          <van-tab
            v-for="(pdf, index) in pdfList"
            :key="index"
            :title="pdf.name || `PDF ${index + 1}`"
          >
            <div class="tab-content">
              <div class="contract-info">
                <p class="contract-name">{{ pdf.name }}</p>
                <p v-if="pdf.contractNo" class="contract-no">合同编号: {{ pdf.contractNo }}</p>
              </div>
            </div>
          </van-tab>
        </van-tabs>
      </div>

      <div class="pdf-content">
        <div v-if="loading" class="loading-container">
          <van-loading type="spinner" color="#1989fa" size="24px">
            正在加载PDF文件...
          </van-loading>
        </div>
        <div v-if="error" class="error-container">
          <p>{{ error }}</p>
          <van-button type="default" @click="retryLoad">重新加载</van-button>
        </div>
        <div v-if="pdfUrl" class="pdf-viewer" :class="{ 'pdf-loading': loading }">
          <pdf
            :key="`pdf-${currentPdfIndex}-${pdfUrl}`"
            :src="pdfUrl"
            :page="currentPage"
            @num-pages="onNumPages"
            @loaded="onPdfLoaded"
            @error="onPdfError"
            class="pdf-page"
          />

           <!-- 在最后一页显示确认按钮 -->
          <div v-if="currentPage === totalPages && !showSignature" class="confirm-section">
            <div class="confirm-content">
              <h3>合同确认</h3>
              <p>请仔细阅读合同内容，确认无误后点击下方按钮进行签名确认。</p>
              <van-button type="primary" @click="showConfirmDialog" class="custom-confirm-button">
                确认并签名
              </van-button>
            </div>
          </div>

           <!-- 手写签名区域 -->
          <div v-if="showSignature" class="signature-section">
            <div class="signature-content">
              <h3>电子签名</h3>
              <p class="signature-tip">请在下方区域进行手写签名，建议将设备横向放置以获得更好的签名体验：</p>
              <div class="signature-canvas-wrapper">
                <canvas
                  ref="signatureCanvas"
                  class="signature-canvas"
                  @touchstart="startDrawing"
                  @touchmove="draw"
                  @touchend="stopDrawing"
                  @mousedown="startDrawing"
                  @mousemove="draw"
                  @mouseup="stopDrawing"
                  @mouseleave="stopDrawing"
                ></canvas>
              </div>
              <div class="signature-actions">
                <van-button type="default" @click="clearSignature" size="large">清除</van-button>
                <van-button type="default" @click="cancelSignature" size="large">取消</van-button>
                <van-button type="primary" @click="confirmSignature" :disabled="!hasSignature" size="large">确认签名</van-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <van-dialog
      v-model="showConfirmModal"
      title="合同确认"
      message="您确认已仔细阅读合同内容并同意签署此合同吗？"
      show-cancel-button
      cancel-button-text="取消"
      confirm-button-text="确认签名"
      cancel-button-color="#969799"
      confirm-button-color="#1989fa"
      @confirm="openSignatureDialog"
      @cancel="closeConfirmDialog"
      class-name="custom-dialog"
    />

    <!-- 签名弹窗 -->
    <van-dialog
      v-model="showSignatureDialog"
      title="合同签名"
      :show-confirm-button="false"
      :show-cancel-button="false"
      :close-on-click-overlay="false"
      class="signature-dialog"
    >
      <SignatureDialog
        @confirm="onSignatureConfirm"
        @cancel="onSignatureCancel"
      />
    </van-dialog>
  </div>
</template>

<script>
import pdf from 'vue-pdf'
import SignatureDialog from '@/components/SignatureDialog.vue'
import requestHTAPI from '@/axios/HantangTax'
import { mapActions, mapGetters } from 'vuex'

export default {
  name: 'Contract',
  components: {
    pdf,
    SignatureDialog
  },
  data () {
    return {
      pdfUrl: '', // 当前PDF文件URL
      contractData: null, // 合同数据
      pdfList: [], // PDF文件列表
      currentPdfIndex: 0, // 当前PDF索引
      showTabView: true, // 是否显示标签页视图
      loading: false, // 初始为false，让PDF组件能够渲染
      error: null,
      currentPage: 1,
      totalPages: 0,
      showSignature: false,
      showConfirmModal: false,
      showSignatureDialog: false,
      isDrawing: false,
      hasSignature: false,
      signatureData: null,
      canvas: null,
      ctx: null,
      lastX: 0,
      lastY: 0,
      switchingPdf: false, // 添加PDF切换状态标识
      switchTimer: null // 切换防抖定时器
    }
  },
  mounted () {
    this.loadContractInfo()

    // 检查是否有保存的人脸识别会话数据
    this.checkFaceSessionData()

    // 监听窗口大小变化 - 主要用于签名画布在设备旋转或窗口调整时重新适配尺寸
    window.addEventListener('resize', this.handleResize)
    window.addEventListener('orientationchange', this.handleResize)
  },
  computed: {
    ...mapGetters([
      'getContractData',
      'getFaceSessionData'
    ])
  },
  beforeDestroy () {
    // 清理资源
    window.removeEventListener('resize', this.handleResize)
    window.removeEventListener('orientationchange', this.handleResize)

    // 清理定时器
    if (this.switchTimer) {
      clearTimeout(this.switchTimer)
      this.switchTimer = null
    }
  },
  methods: {
    ...mapActions([
      'saveContractData',
      'saveSignatureData',
      'saveFaceSessionData',
      'clearAllContractData'
    ]),
    loadContractInfo () {
      try {
        // 从Vuex获取合同数据
        const contractData = this.getContractData
        if (contractData) {
          this.contractData = contractData

          // 初始化PDF列表
          if (this.contractData.dataList && this.contractData.dataList.length > 0) {
            this.pdfList = this.contractData.dataList.map((item, index) => ({
              url: item.path,
              name: item.levyName || `合同文件 ${index + 1}`,
              contractNo: item.contractNo,
              levyId: item.levyId
            }))

            // 设置第一个PDF为当前显示的PDF
            if (this.pdfList.length > 0) {
              this.pdfUrl = this.pdfList[0].url
              this.currentPdfIndex = 0
              this.loading = true // 开始加载PDF
            }
          } else {
            // 如果没有PDF数据，使用默认PDF
            this.pdfUrl = this.$route.query.pdfUrl || '/0314e6b7244847fd907fc2dc694aa9d1_电子合同.pdf'
            this.pdfList = [{ url: this.pdfUrl, name: '合同文件' }]
          }
        } else {
          // 从路由参数获取PDF URL（兼容旧版本）
          this.pdfUrl = this.$route.query.pdfUrl || '/0314e6b7244847fd907fc2dc694aa9d1_电子合同.pdf'
          this.pdfList = [{ url: this.pdfUrl, name: '合同文件' }]
          this.loading = true // 开始加载PDF
        }
      } catch (error) {
        console.error('加载合同信息失败:', error)
        this.error = '加载合同信息失败，请重试'
      }
    },
    onPdfLoaded () {
      console.log('PDF加载完成')
      // PDF加载完成，隐藏loading状态
      this.loading = false
      this.error = null

      // 如果是切换PDF后的加载完成，显示成功提示并重置切换状态
      if (this.pdfList.length > 1 && this.switchingPdf) {
        this.$toast && this.$toast('PDF切换成功')
        this.switchingPdf = false
        if (this.switchTimer) {
          clearTimeout(this.switchTimer)
          this.switchTimer = null
        }
      }
    },
    onNumPages (numPages) {
      this.totalPages = numPages
      console.log('总页数:', numPages)
    },
    onPdfError (error) {
      console.error('PDF加载失败:', error)
      this.loading = false
      this.error = 'PDF文件加载失败，请检查网络连接或文件是否存在'
      this.$toast && this.$toast('PDF加载失败，请重试')

      // 如果是多PDF模式，提供切换到其他PDF的选项
      if (this.pdfList.length > 1) {
        console.log('当前PDF索引:', this.currentPdfIndex, '总PDF数量:', this.pdfList.length)
      }
    },

    previousPage () {
      if (this.currentPage > 1) {
        this.currentPage--
      }
    },
    nextPage () {
      if (this.currentPage < this.totalPages) {
        this.currentPage++
      }
    },
    showConfirmDialog () {
      this.showConfirmModal = true
    },
    closeConfirmDialog () {
      this.showConfirmModal = false
    },
    // 标签页相关方法
    toggleTabView () {
      this.showTabView = !this.showTabView
    },
    onTabClick (index) {
      if (index !== this.currentPdfIndex && !this.switchingPdf) {
        // 防止快速切换导致的问题
        this.switchingPdf = true

        // 清除之前的定时器
        if (this.switchTimer) {
          clearTimeout(this.switchTimer)
        }

        // 先清空当前PDF URL，强制组件重新渲染
        this.pdfUrl = ''
        this.loading = true
        this.error = null
        this.currentPage = 1 // 重置到第一页
        this.totalPages = 0

        // 使用nextTick确保DOM更新后再设置新的PDF
        this.$nextTick(() => {
          this.currentPdfIndex = index
          this.pdfUrl = this.pdfList[index].url

          // 添加切换提示
          this.$toast && this.$toast('正在切换PDF文件...')

          // 设置切换完成的定时器
          this.switchTimer = setTimeout(() => {
            this.switchingPdf = false
          }, 1000)
        })
      }
    },
    getCurrentPdfName () {
      if (this.pdfList.length > 0 && this.currentPdfIndex < this.pdfList.length) {
        return this.pdfList[this.currentPdfIndex].name
      }
      return '合同文件'
    },
    retryLoad () {
      this.loading = true
      this.error = null
      this.$toast && this.$toast('正在重新加载...')

      // 重新加载当前PDF，强制刷新
      const currentUrl = this.pdfUrl
      this.pdfUrl = ''
      this.currentPage = 1
      this.totalPages = 0

      this.$nextTick(() => {
        this.pdfUrl = currentUrl + '?t=' + Date.now() // 添加时间戳避免缓存
      })
    },
    startSignature () {
      this.showConfirmModal = false
      this.openSignatureDialog()
    },
    openSignatureDialog () {
      this.showSignatureDialog = true
    },
    onSignatureConfirm (signatureData) {
      this.signatureData = signatureData
      this.showSignatureDialog = false

      this.$toast('签名成功！')
      // 签名成功后调用人脸识别接口
      this.callFxqFaceAPI()
    },
    onSignatureCancel () {
      this.showSignatureDialog = false
      console.log('用户取消签名')
    },
    initSignatureCanvas () {
      const canvas = this.$refs.signatureCanvas
      if (canvas) {
        const ctx = canvas.getContext('2d')
        const container = canvas.parentElement

        // 设置画布尺寸
        const containerWidth = container.clientWidth
        const containerHeight = Math.max(250, window.innerHeight * 0.3)

        canvas.width = containerWidth
        canvas.height = containerHeight

        // 设置画布的CSS尺寸与实际尺寸一致
        canvas.style.width = canvas.width + 'px'
        canvas.style.height = canvas.height + 'px'

        // 设置画笔样式
        ctx.strokeStyle = '#000000'
        ctx.lineWidth = 2
        ctx.lineCap = 'round'
        ctx.lineJoin = 'round'
        ctx.globalCompositeOperation = 'source-over'

        this.canvas = canvas
        this.ctx = ctx
        this.lastX = 0
        this.lastY = 0
      }
    },
    getEventPos (e) {
      const rect = this.canvas.getBoundingClientRect()
      const clientX = e.clientX || (e.touches && e.touches[0].clientX)
      const clientY = e.clientY || (e.touches && e.touches[0].clientY)

      // 计算缩放比例
      const scaleX = this.canvas.width / rect.width
      const scaleY = this.canvas.height / rect.height

      return {
        x: (clientX - rect.left) * scaleX,
        y: (clientY - rect.top) * scaleY
      }
    },
    startDrawing (event) {
      event.preventDefault()
      event.stopPropagation()
      this.isDrawing = true
      const pos = this.getEventPos(event)
      this.lastX = pos.x
      this.lastY = pos.y

      // 开始新路径
      this.ctx.beginPath()
      this.ctx.moveTo(this.lastX, this.lastY)

      // 绘制起始点
      this.ctx.lineTo(pos.x, pos.y)
      this.ctx.stroke()
      this.ctx.beginPath()
      this.ctx.moveTo(pos.x, pos.y)
    },
    draw (event) {
      if (!this.isDrawing) return
      event.preventDefault()
      event.stopPropagation()

      const pos = this.getEventPos(event)

      // 使用二次贝塞尔曲线使线条更平滑
      this.ctx.quadraticCurveTo(this.lastX, this.lastY, (pos.x + this.lastX) / 2, (pos.y + this.lastY) / 2)
      this.ctx.stroke()
      this.ctx.beginPath()
      this.ctx.moveTo((pos.x + this.lastX) / 2, (pos.y + this.lastY) / 2)

      this.lastX = pos.x
      this.lastY = pos.y
      this.hasSignature = true
    },
    stopDrawing (event) {
      if (!this.isDrawing) return
      event.preventDefault()
      event.stopPropagation()
      this.isDrawing = false
      this.ctx.closePath()
    },
    clearSignature () {
      if (this.ctx && this.canvas) {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
        this.hasSignature = false
      }
    },
    cancelSignature () {
      this.showSignature = false
      this.hasSignature = false
    },
    confirmSignature () {
      if (!this.hasSignature) {
        this.$toast && this.$toast('请先进行签名')
        return
      }

      const canvas = this.$refs.signatureCanvas
      this.signatureData = canvas.toDataURL()

      // 这里可以将签名数据发送到服务器
      console.log('签名数据:', this.signatureData)

      this.$toast && this.$toast('签名成功！')
      this.showSignature = false

      // 签名成功后调用人脸识别接口
      this.callFxqFaceAPI()
    },
    // 调用人脸识别接口
    callFxqFaceAPI () {
      try {
        // 从合同数据中获取参数
        const params = {
          id: this.contractData?.id || '',
          merId: this.contractData?.merId || ''
        }

        console.log('调用fxqFace接口，参数:', params)

        requestHTAPI.fxqFace(params).then(res => {
          console.log('fxqFace接口响应:', res)
          if (res.data.code === '0000') {
            this.$toast && this.$toast('正在跳转到人脸识别页面...')

            // 从data字段中获取URL并跳转
            const faceUrl = res.data.data
            if (faceUrl) {
              // 保存当前会话数据到Vuex
              const sessionData = {
                contractData: this.contractData,
                signatureData: this.signatureData,
                currentPage: this.currentPage,
                currentPdfIndex: this.currentPdfIndex
              }
              this.saveContractData(this.contractData)
              this.saveSignatureData(this.signatureData)
              this.saveFaceSessionData(sessionData)

              // 监听页面可见性变化，检测用户是否从人脸识别页面返回
              const handleVisibilityChange = () => {
                if (!document.hidden) {
                  // 页面重新可见，延迟一段时间后调用fxqTwoStep接口
                  setTimeout(() => {
                    this.callFxqTwoStepAPI()
                    document.removeEventListener('visibilitychange', handleVisibilityChange)
                  }, 1000)
                }
              }
              document.addEventListener('visibilitychange', handleVisibilityChange)

              // 跳转到人脸识别页面
              window.location.href = faceUrl
            } else {
              this.$toast && this.$toast('未获取到人脸识别链接')
            }
          } else {
            this.$toast && this.$toast(res.data.message || '人脸识别调用失败')
          }
        }).catch(error => {
          console.error('fxqFace接口调用失败:', error)
          this.$toast && this.$toast('人脸识别调用失败，请重试')
        })
      } catch (error) {
        console.error('调用fxqFace接口时发生错误:', error)
        this.$toast && this.$toast('调用人脸识别接口时发生错误')
      }
    },
    // 检查人脸识别会话数据
    checkFaceSessionData () {
      try {
        const sessionData = this.getFaceSessionData
        if (sessionData) {
          console.log('检测到人脸识别会话数据，准备调用fxqTwoStep接口')
          // 延迟调用，确保页面完全加载
          setTimeout(() => {
            this.callFxqTwoStepAPI()
          }, 2000)
        }
      } catch (error) {
        console.error('检查人脸识别会话数据时发生错误:', error)
      }
    },
    // 调用人脸识别第二步接口
    callFxqTwoStepAPI () {
      try {
        // 从Vuex恢复会话数据
        const sessionData = this.getFaceSessionData
        if (!sessionData) {
          console.error('未找到会话数据')
          this.$toast && this.$toast('会话数据丢失，请重新签名')
          return
        }
        const dataList = sessionData.contractData?.dataList || []

        const params = {
          base64: sessionData.signatureData || '', // 手写签名的图片
          dataList: dataList,
          id: sessionData.contractData?.id || ''
        }

        console.log('调用fxqTwoStep接口，参数:', params)

        requestHTAPI.fxqTwoStep(params).then(res => {
          console.log('fxqTwoStep接口响应:', res)
          if (res.data.code === '0000') {
            this.$toast && this.$toast('合同签署完成！')
            // 清除会话数据
            this.clearAllContractData()
            // 可以跳转到成功页面
            // this.$router.push('/contract/success')
          } else {
            this.$toast && this.$toast(res.data.message || '合同签署失败')
          }
        }).catch(error => {
          console.error('fxqTwoStep接口调用失败:', error)
          this.$toast && this.$toast('合同签署失败，请重试')
        })
      } catch (error) {
        console.error('调用fxqTwoStep接口时发生错误:', error)
        this.$toast && this.$toast('调用合同签署接口时发生错误')
      }
    },
    downloadContract () {
      if (this.pdfUrl) {
        // 创建下载链接
        const link = document.createElement('a')
        link.href = this.pdfUrl
        link.download = 'contract.pdf'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        this.$toast && this.$toast('PDF文件不存在')
      }
    },
    retryLoadPdf () {
      this.loading = true
      this.error = null
      // 重新加载PDF
      this.loadContractInfo()
    },
    goBack () {
      this.$router.go(-1)
    },
    handleResize () {
      // 延迟执行以确保DOM更新完成
      setTimeout(() => {
        if (this.canvas && this.ctx && this.showSignature) {
          // 保存当前绘制内容
          const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height)

          // 重新初始化画布
          this.initSignatureCanvas()

          // 如果有签名内容，尝试恢复
          if (this.hasSignature) {
            this.ctx.putImageData(imageData, 0, 0)
          }
        }
      }, 100)
    }
  }
}
</script>

<style scoped>
.contract-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.pdf-viewer-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  margin: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-info,
.page-info {
  font-size: 14px;
  color: #666;
  min-width: 60px;
  text-align: center;
}

.pdf-content {
  flex: 1;
  width: 100%;
  height: 100%;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
}

.pdf-viewer {
  width: 100%;
  height: 100%;
  position: relative;
}

.pdf-viewer.pdf-loading {
  opacity: 0.3;
  pointer-events: none;
}

.pdf-page {
  width: 100%;
  height: 100%;
}

/* 确认按钮区域 */
.confirm-section {
  width: 100%;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

.confirm-content {
  text-align: center;
}

.confirm-content h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 18px;
}

.confirm-content p {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.5;
}

/* 签名区域 */
.signature-section {
  width: 100%;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

.signature-content {
  text-align: center;
}

.signature-content h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 18px;
}

.signature-tip {
  color: #666;
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.5;
}

.signature-canvas-wrapper {
  background: white;
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.signature-canvas-wrapper::before {
  content: '签名区域';
  position: absolute;
  top: 15px;
  left: 15px;
  color: #ccc;
  font-size: 12px;
  pointer-events: none;
  z-index: 1;
}

.signature-canvas {
  width: 100%;
  height: 250px;
  border: 1px solid #eee;
  border-radius: 4px;
  cursor: crosshair;
  touch-action: none;
  display: block;
  background: linear-gradient(90deg, transparent 24px, #f0f0f0 25px, #f0f0f0 26px, transparent 27px),
              linear-gradient(#f9f9f9 24px, transparent 25px, transparent 26px, #f9f9f9 27px);
  background-size: 25px 25px;
}

.signature-canvas:hover {
  border-color: #1989fa;
}

.signature-canvas:focus {
  outline: none;
  border-color: #1989fa;
  box-shadow: 0 0 0 2px rgba(25, 137, 250, 0.2);
}

.signature-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.signature-actions .van-button {
  flex: 1;
  max-width: 120px;
  height: 45px;
  font-size: 15px;
  border-radius: 8px;
}

.contract-actions {
  padding: 15px 20px;
  background: white;
  border-top: 1px solid #e8e8e8;
  display: flex;
  justify-content: center;
}

/* 签名弹窗样式 */
.signature-dialog {
  width: 90vw !important;
  max-width: 800px !important;
  height: auto !important;
  max-height: 90vh !important;

  .van-dialog__content {
    padding: 0;
  }
}

/* PDF工具栏样式 */
.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.toolbar-left, .toolbar-right {
  display: flex;
  gap: 8px;
  align-items: center;
}

.toolbar-center {
  flex: 1;
  text-align: center;
  padding: 0 15px;
}

.pdf-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* PDF标签页样式 */
.pdf-tabs-container {
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.tab-content {
  padding: 15px 20px;
  background: #f8f9fa;
}

.contract-info {
  text-align: center;
}

.contract-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0 0 8px 0;
}

.contract-no {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* 自定义标签页样式 */
.pdf-tabs-container .van-tabs__wrap {
  background: white;
}

.pdf-tabs-container .van-tab {
  font-size: 14px;
  color: #666;
}

.pdf-tabs-container .van-tab--active {
  color: #1989fa;
  font-weight: 500;
}

.pdf-tabs-container .van-tabs__line {
  background-color: #1989fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar-center {
    display: none;
  }

  .pdf-title {
    display: none;
  }

  .pdf-tabs-container .van-tab {
    font-size: 12px;
  }

  .contract-name {
    font-size: 14px;
  }

  .contract-no {
    font-size: 12px;
  }
}
</style>

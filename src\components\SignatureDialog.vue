<template>
  <div class="signature-content">
    <p class="signature-tip">{{ tip }}</p>

    <div class="signature-canvas-wrapper">
      <canvas
        ref="signatureCanvas"
        class="signature-canvas"
        @touchstart="startDrawing"
        @touchmove="draw"
        @touchend="stopDrawing"
        @mousedown="startDrawing"
        @mousemove="draw"
        @mouseup="stopDrawing"
        @mouseleave="stopDrawing"
      ></canvas>
    </div>

    <div class="signature-actions">
      <van-button plain @click="clearSignature">
        清除
      </van-button>
      <van-button plain @click="cancel">
        取消
      </van-button>
      <van-button
        type="primary"
        @click="confirm"
        :disabled="!hasSignature"
      >
        确认签名
      </van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SignatureDialog',
  props: {
    tip: {
      type: String,
      default: '请在下方区域进行手写签名，建议将设备横向放置以获得更好的签名体验'
    }
  },
  data () {
    return {
      isDrawing: false,
      hasSignature: false,
      canvas: null,
      ctx: null,
      lastX: 0,
      lastY: 0
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initCanvas()
    })
  },
  methods: {
    initCanvas () {
      this.canvas = this.$refs.signatureCanvas
      this.ctx = this.canvas.getContext('2d')

      const container = this.canvas.parentElement
      const containerWidth = container.clientWidth
      // 增加高度占比，使签名区域尽量填满全屏
      const containerHeight = Math.max(300, window.innerHeight * 0.6)

      this.canvas.width = containerWidth
      this.canvas.height = containerHeight

      this.canvas.style.width = this.canvas.width + 'px'
      this.canvas.style.height = this.canvas.height + 'px'

      this.ctx.strokeStyle = '#000000'
      this.ctx.lineWidth = 2
      this.ctx.lineCap = 'round'
      this.ctx.lineJoin = 'round'
      this.ctx.globalCompositeOperation = 'source-over'
    },

    getEventPos (e) {
      const rect = this.canvas.getBoundingClientRect()
      const clientX = e.clientX || (e.touches && e.touches[0].clientX)
      const clientY = e.clientY || (e.touches && e.touches[0].clientY)

      const scaleX = this.canvas.width / rect.width
      const scaleY = this.canvas.height / rect.height

      return {
        x: (clientX - rect.left) * scaleX,
        y: (clientY - rect.top) * scaleY
      }
    },

    startDrawing (e) {
      e.preventDefault()
      e.stopPropagation()
      this.isDrawing = true
      const pos = this.getEventPos(e)
      this.lastX = pos.x
      this.lastY = pos.y

      this.ctx.beginPath()
      this.ctx.moveTo(this.lastX, this.lastY)
      this.ctx.lineTo(pos.x, pos.y)
      this.ctx.stroke()
      this.ctx.beginPath()
      this.ctx.moveTo(pos.x, pos.y)
    },

    draw (e) {
      if (!this.isDrawing) return
      e.preventDefault()
      e.stopPropagation()

      const pos = this.getEventPos(e)
      this.ctx.quadraticCurveTo(this.lastX, this.lastY, (pos.x + this.lastX) / 2, (pos.y + this.lastY) / 2)
      this.ctx.stroke()
      this.ctx.beginPath()
      this.ctx.moveTo((pos.x + this.lastX) / 2, (pos.y + this.lastY) / 2)

      this.lastX = pos.x
      this.lastY = pos.y
      this.hasSignature = true
    },

    stopDrawing (e) {
      if (!this.isDrawing) return
      e.preventDefault()
      e.stopPropagation()
      this.isDrawing = false
      this.ctx.closePath()
    },

    clearSignature () {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
      this.hasSignature = false
    },

    cancel () {
      this.clearSignature()
      this.$emit('cancel')
    },

    confirm () {
      if (!this.hasSignature) {
        this.$toast('请先进行签名')
        return
      }

      const signatureData = this.canvas.toDataURL('image/png')
      // 去除data:image/png;base64,前缀，只保留纯base64数据
      const base64Data = signatureData.replace(/^data:image\/png;base64,/, '')
      this.$emit('confirm', base64Data)
    }
  }
}
</script>

<style lang="less">
.signature-content {
    padding: 16px;

    .signature-tip {
      color: var(--van-text-color-2);
      font-size: 14px;
      line-height: 1.5;
      margin-bottom: 16px;
      text-align: center;
    }

    .signature-canvas-wrapper {
      background: white;
      border: 1px solid var(--van-border-color);
      border-radius: var(--van-border-radius-md);
      margin-bottom: 16px;
      height: calc(100% - 120px);
      min-height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;

      .signature-canvas {
        width: 100%;
        height: 100%;
        min-height: 300px;
        cursor: crosshair;
        touch-action: none;
        display: block;
      }
    }

    .signature-actions {
      display: flex;
      gap: 10px;
      justify-content: center;

      .van-button {
        flex: 1;
      }
    }
  }

</style>
